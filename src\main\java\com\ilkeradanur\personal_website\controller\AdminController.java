package com.ilkeradanur.personal_website.controller;

import com.ilkeradanur.personal_website.entity.Project;
import com.ilkeradanur.personal_website.entity.Skill;
import com.ilkeradanur.personal_website.entity.ProjectStatus;
import com.ilkeradanur.personal_website.entity.Timeline;
import com.ilkeradanur.personal_website.service.ProjectService;
import com.ilkeradanur.personal_website.service.SkillService;
import com.ilkeradanur.personal_website.service.AboutService;
import com.ilkeradanur.personal_website.service.TimelineService;
import com.ilkeradanur.personal_website.entity.About;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;
import org.springframework.web.multipart.MultipartFile;

@Controller
@RequestMapping("/admin")
public class AdminController {

    private final SkillService skillService;
    private final ProjectService projectService;
    private final AboutService aboutService;
    private final TimelineService timelineService;

    @Autowired
    public AdminController(SkillService skillService, ProjectService projectService, AboutService aboutService, TimelineService timelineService) {
        this.skillService = skillService;
        this.projectService = projectService;
        this.aboutService = aboutService;
        this.timelineService = timelineService;
    }

    @GetMapping("/dashboard")
    public String dashboard(Model model) {
        model.addAttribute("title", "Admin Paneli");
        return "admin/dashboard";
    }

    // Yetenekler Yönetimi
    @GetMapping("/skills")
    public String skills(Model model) {
        model.addAttribute("title", "Yetenekler Düzenle");
        model.addAttribute("skills", skillService.getAllSkills());
        model.addAttribute("skill", new Skill());
        return "admin/skills";
    }

    @PostMapping("/skills")
    public String saveSkill(@ModelAttribute Skill skill, RedirectAttributes redirectAttributes) {
        try {
            skillService.saveSkill(skill);
            redirectAttributes.addFlashAttribute("success", "Yetenek başarıyla kaydedildi.");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Yetenek kaydedilirken bir hata oluştu.");
        }
        return "redirect:/admin/skills";
    }

    @PostMapping("/skills/{id}/delete")
    public String deleteSkill(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        try {
            skillService.deleteSkill(id);
            redirectAttributes.addFlashAttribute("success", "Yetenek başarıyla silindi.");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Yetenek silinirken bir hata oluştu.");
        }
        return "redirect:/admin/skills";
    }

    // Projeler Yönetimi
    @GetMapping("/projects")
    public String projects(Model model) {
        model.addAttribute("title", "Projeler Düzenle");
        model.addAttribute("projects", projectService.getAllProjects());
        model.addAttribute("project", new Project());
        model.addAttribute("projectStatuses", com.ilkeradanur.personal_website.entity.ProjectStatus.values());
        return "admin/projects";
    }

    @PostMapping("/projects")
    public String saveProject(@ModelAttribute Project project, RedirectAttributes redirectAttributes) {
        try {
            projectService.saveProject(project);
            redirectAttributes.addFlashAttribute("success", "Proje başarıyla kaydedildi.");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Proje kaydedilirken bir hata oluştu.");
        }
        return "redirect:/admin/projects";
    }

    @PostMapping("/projects/{id}/delete")
    public String deleteProject(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        try {
            projectService.deleteProject(id);
            redirectAttributes.addFlashAttribute("success", "Proje başarıyla silindi.");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Proje silinirken bir hata oluştu.");
        }
        return "redirect:/admin/projects";
    }

    // Hakkımda Yönetimi
    @GetMapping("/about")
    public String about(Model model) {
        model.addAttribute("title", "Hakkımda Düzenle");
        aboutService.getActiveAbout().ifPresent(about -> model.addAttribute("about", about));
        if (!model.containsAttribute("about")) {
            model.addAttribute("about", new About());
        }
        return "admin/about";
    }

    @PostMapping("/about")
    public String saveAbout(@ModelAttribute About about,
                          @RequestParam(required = false) MultipartFile profileImage,
                          @RequestParam(required = false) MultipartFile resume,
                          RedirectAttributes redirectAttributes) {
        try {
            aboutService.saveAbout(about, profileImage, resume);
            redirectAttributes.addFlashAttribute("success", "Hakkımda bilgileri başarıyla kaydedildi.");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Hakkımda bilgileri kaydedilirken bir hata oluştu: " + e.getMessage());
        }
        return "redirect:/admin/about";
    }

    // Zaman Çizelgesi Yönetimi
    @GetMapping("/timeline")
    public String timeline(Model model) {
        model.addAttribute("title", "Zaman Çizelgesi Düzenle");
        model.addAttribute("timelineItems", timelineService.getAllTimeline());
        model.addAttribute("timeline", new Timeline());
        return "admin/timeline";
    }

    @PostMapping("/timeline")
    public String saveTimelineItem(@ModelAttribute Timeline timeline, RedirectAttributes redirectAttributes) {
        try {
            timelineService.saveTimeline(timeline);
            redirectAttributes.addFlashAttribute("success", "Zaman çizelgesi kaydı başarıyla eklendi.");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Zaman çizelgesi kaydı eklenirken bir hata oluştu: " + e.getMessage());
        }
        return "redirect:/admin/timeline";
    }

    @PostMapping("/timeline/{id}/delete")
    public String deleteTimelineItem(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        try {
            timelineService.deleteTimeline(id);
            redirectAttributes.addFlashAttribute("success", "Zaman çizelgesi kaydı başarıyla silindi.");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Zaman çizelgesi kaydı silinirken bir hata oluştu.");
        }
        return "redirect:/admin/timeline";
    }
}

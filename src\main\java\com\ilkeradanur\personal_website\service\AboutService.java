package com.ilkeradanur.personal_website.service;

import com.ilkeradanur.personal_website.entity.About;
import com.ilkeradanur.personal_website.repository.AboutRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Optional;
import java.util.UUID;

@Service
@Transactional
public class AboutService {

    private final AboutRepository aboutRepository;
    private final Path uploadDir;

    @Autowired
    public AboutService(AboutRepository aboutRepository) {
        this.aboutRepository = aboutRepository;
        this.uploadDir = Paths.get("uploads");
        try {
            if (!Files.exists(uploadDir)) {
                Files.createDirectories(uploadDir);
            }
        } catch (IOException e) {
            throw new RuntimeException("Upload dizini oluşturulamad<PERSON>!", e);
        }
    }

    public Optional<About> getActiveAbout() {
        return aboutRepository.findByIsActiveTrue();
    }

    public About saveAbout(About about, MultipartFile profileImage, MultipartFile resume) throws IOException {
        // Aktif kayıt varsa pasife çek
        aboutRepository.findByIsActiveTrue().ifPresent(existingAbout -> {
            existingAbout.setIsActive(false);
            aboutRepository.save(existingAbout);
        });

        // Yeni kaydı aktif yap
        about.setIsActive(true);

        // Profil resmi yükleme
        if (profileImage != null && !profileImage.isEmpty()) {
            String fileName = saveFile(profileImage, "profile");
            about.setProfileImage(fileName);
        }

        // CV yükleme
        if (resume != null && !resume.isEmpty()) {
            String fileName = saveFile(resume, "resume");
            about.setResumeFile(fileName);
        }

        return aboutRepository.save(about);
    }

    private String saveFile(MultipartFile file, String prefix) throws IOException {
        String originalFilename = file.getOriginalFilename();
        String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        String fileName = prefix + "_" + UUID.randomUUID().toString() + extension;
        Path filePath = uploadDir.resolve(fileName);
        Files.copy(file.getInputStream(), filePath);
        return fileName;
    }

    public void deleteFile(String fileName) {
        try {
            Path filePath = uploadDir.resolve(fileName);
            Files.deleteIfExists(filePath);
        } catch (IOException e) {
            throw new RuntimeException("Dosya silinemedi: " + fileName, e);
        }
    }
}

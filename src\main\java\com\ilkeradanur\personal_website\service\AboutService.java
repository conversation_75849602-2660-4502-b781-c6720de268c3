package com.ilkeradanur.personal_website.service;

import com.ilkeradanur.personal_website.entity.About;
import com.ilkeradanur.personal_website.repository.AboutRepository;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Optional;

@Service
public class AboutService {

    private static final Logger logger = LoggerFactory.getLogger(AboutService.class);
    private final AboutRepository aboutRepository;

    public AboutService(AboutRepository aboutRepository) {
        this.aboutRepository = aboutRepository;
    }

    public Optional<About> getAbout() {
        return aboutRepository.findFirst();
    }

    public About saveAbout(About about) {
        // Null kontrolü
        if (about.getFullName() == null || about.getFullName().trim().isEmpty()) {
            throw new IllegalArgumentException("Ad Soyad alanı boş olamaz");
        }
        if (about.getTitle() == null || about.getTitle().trim().isEmpty()) {
            throw new IllegalArgumentException("Başlık alanı boş olamaz");
        }
        if (about.getAboutText() == null || about.getAboutText().trim().isEmpty()) {
            throw new IllegalArgumentException("Hakkımda metni boş olamaz");
        }

        logger.info("About bilgisi kaydediliyor: {}", about.getFullName());

        // Eğer kayıt varsa güncelle, yoksa yeni oluştur
        Optional<About> existingAbout = aboutRepository.findFirst();
        if (existingAbout.isPresent()) {
            About existing = existingAbout.get();
            existing.setFullName(about.getFullName());
            existing.setTitle(about.getTitle());
            existing.setAboutText(about.getAboutText());
            return aboutRepository.save(existing);
        } else {
            return aboutRepository.save(about);
        }
    }

    public void initializeAbout() {
        // Eğer hiç about kaydı yoksa varsayılan oluştur
        if (aboutRepository.count() == 0) {
            About defaultAbout = new About(
                "İlker Adanur",
                "Full Stack Developer",
                "Merhaba! Ben İlker Adanur, tutkulu bir yazılım geliştiricisiyim. Java, Spring Boot, React ve modern web teknolojileri konusunda deneyimliyim. Sürekli öğrenmeye ve kendimi geliştirmeye odaklanıyorum."
            );
            aboutRepository.save(defaultAbout);
            logger.info("Varsayılan About kaydı oluşturuldu");
        }
    }
}

package com.ilkeradanur.personal_website.service;

import com.ilkeradanur.personal_website.entity.About;
import com.ilkeradanur.personal_website.repository.AboutRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Optional;
import java.util.List;
import java.util.UUID;

@Service
@Transactional
public class AboutService {

    private final AboutRepository aboutRepository;
    private final Path uploadDir;

    @Autowired
    public AboutService(AboutRepository aboutRepository) {
        this.aboutRepository = aboutRepository;
        this.uploadDir = Paths.get("uploads");
        try {
            if (!Files.exists(uploadDir)) {
                Files.createDirectories(uploadDir);
            }
        } catch (IOException e) {
            throw new RuntimeException("Upload dizini o<PERSON>!", e);
        }
    }

    public Optional<About> getActiveAbout() {
        return aboutRepository.findFirstByIsActiveTrueOrderByIdDesc();
    }

    public About saveAbout(About about, MultipartFile profileImage, MultipartFile resume) throws IOException {
        // Null kontrolü
        if (about.getFullName() == null || about.getFullName().trim().isEmpty()) {
            throw new IllegalArgumentException("Ad Soyad alanı boş olamaz");
        }
        if (about.getTitle() == null || about.getTitle().trim().isEmpty()) {
            throw new IllegalArgumentException("Başlık alanı boş olamaz");
        }
        if (about.getEmail() == null || about.getEmail().trim().isEmpty()) {
            throw new IllegalArgumentException("Email alanı boş olamaz");
        }

        // Eğer ID varsa güncelleme, yoksa yeni kayıt
        if (about.getId() != null) {
            // Güncelleme işlemi
            About existingAbout = aboutRepository.findById(about.getId())
                .orElseThrow(() -> new RuntimeException("Güncellenecek kayıt bulunamadı"));

            // Mevcut değerleri koru
            if (about.getProfileImage() == null) {
                about.setProfileImage(existingAbout.getProfileImage());
            }
            if (about.getResumeFile() == null) {
                about.setResumeFile(existingAbout.getResumeFile());
            }
        } else {
            // Yeni kayıt için tüm aktif kayıtları pasife çek
            List<About> activeAbouts = aboutRepository.findByIsActiveTrue();
            for (About activeAbout : activeAbouts) {
                activeAbout.setIsActive(false);
                aboutRepository.save(activeAbout);
            }
        }

        // Yeni kaydı aktif yap
        about.setIsActive(true);

        // Profil resmi yükleme
        if (profileImage != null && !profileImage.isEmpty()) {
            String fileName = saveFile(profileImage, "profile");
            about.setProfileImage(fileName);
        }

        // CV yükleme
        if (resume != null && !resume.isEmpty()) {
            String fileName = saveFile(resume, "resume");
            about.setResumeFile(fileName);
        }

        return aboutRepository.save(about);
    }

    private String saveFile(MultipartFile file, String prefix) throws IOException {
        String originalFilename = file.getOriginalFilename();
        String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        String fileName = prefix + "_" + UUID.randomUUID().toString() + extension;
        Path filePath = uploadDir.resolve(fileName);
        Files.copy(file.getInputStream(), filePath);
        return fileName;
    }

    public void deleteFile(String fileName) {
        try {
            Path filePath = uploadDir.resolve(fileName);
            Files.deleteIfExists(filePath);
        } catch (IOException e) {
            throw new RuntimeException("Dosya silinemedi: " + fileName, e);
        }
    }
}

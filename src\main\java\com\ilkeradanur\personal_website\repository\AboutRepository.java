package com.ilkeradanur.personal_website.repository;

import com.ilkeradanur.personal_website.entity.About;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.Optional;
import java.util.List;

@Repository
public interface AboutRepository extends JpaRepository<About, Long> {
    @Query(value = "SELECT * FROM about WHERE is_active = 1 ORDER BY id DESC LIMIT 1", nativeQuery = true)
    Optional<About> findFirstByIsActiveTrueOrderByIdDesc();

    List<About> findByIsActiveTrue();
}

package com.ilkeradanur.personal_website.repository;

import com.ilkeradanur.personal_website.entity.About;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.Optional;
import java.util.List;

@Repository
public interface AboutRepository extends JpaRepository<About, Long> {
    Optional<About> findFirstByIsActiveTrueOrderByIdDesc();
    List<About> findByIsActiveTrue();
}

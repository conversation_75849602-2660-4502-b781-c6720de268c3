package com.ilkeradanur.personal_website.config;

import com.ilkeradanur.personal_website.entity.User;
import com.ilkeradanur.personal_website.repository.UserRepository;
import com.ilkeradanur.personal_website.service.AboutService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
public class DataInitializer implements CommandLineRunner {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private AboutService aboutService;

    @Override
    @Transactional
    public void run(String... args) {
        try {
            System.out.println("DEBUG: DataInitializer başlatılıyor...");

            // Admin kullanıcısının var olup olmadığını kontrol et
            boolean adminExists = userRepository.findByUsername("admin").isPresent();
            System.out.println("DEBUG: Ad<PERSON> kull<PERSON>ı mevcut mu? " + adminExists);

            if (!adminExists) {
                // Admin kullanıcısı oluştur
                User adminUser = new User();
                adminUser.setUsername("admin");
                adminUser.setPassword("admin"); // Şifreleme olmadığı için düz metin şifre
                adminUser.setRole("ROLE_ADMIN");
                adminUser.setEnabled(true);

                User savedUser = userRepository.save(adminUser);
                userRepository.flush(); // Değişiklikleri hemen veritabanına yaz
                System.out.println("DEBUG: Admin kullanıcısı oluşturuldu - ID: " + savedUser.getId() +
                                 ", Username: " + savedUser.getUsername() +
                                 ", Password: " + savedUser.getPassword() +
                                 ", Role: " + savedUser.getRole() +
                                 ", Enabled: " + savedUser.isEnabled());
            } else {
                System.out.println("DEBUG: Admin kullanıcısı zaten mevcut.");
            }

            // About bilgisini başlat
            aboutService.initializeAbout();

        } catch (Exception e) {
            System.err.println("Veritabanı başlatma hatası: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
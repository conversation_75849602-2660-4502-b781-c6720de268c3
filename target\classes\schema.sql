-- SQLite schema for personal website

CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(255) NOT NULL,
    enabled BOOLEAN NOT NULL
);

CREATE TABLE IF NOT EXISTS skills (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) NOT NULL,
    category VARCHAR(255),
    description VARCHAR(1000),
    proficiency INTEGER,
    icon VARCHAR(255),
    certification_name VARCHAR(255),
    certification_url VARCHAR(255),
    issuer VARCHAR(255),
    issue_date VARCHAR(255),
    expiry_date VARCHAR(255),
    is_active BOOLEAN NOT NULL DEFAULT true
);

CREATE TABLE IF NOT EXISTS projects (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title VARCHAR(255) NOT NULL,
    description VARCHAR(1000),
    start_date DATE,
    end_date DATE,
    status VARCHAR(255) NOT NULL CHECK (status IN ('IN_PROGRESS','COMPLETED','ON_HOLD','CANCELLED')),
    github_link VARCHAR(255),
    demo_link VARCHAR(255),
    category VARCHAR(255),
    technologies VARCHAR(255)
);



CREATE TABLE IF NOT EXISTS about (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    full_name VARCHAR(255) NOT NULL,
    title VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(255),
    location VARCHAR(255),
    birth_date DATE,
    about_text TEXT,
    profile_image VARCHAR(255),
    resume_file VARCHAR(255),
    github_url VARCHAR(255),
    linkedin_url VARCHAR(255),
    twitter_url VARCHAR(255),
    website_url VARCHAR(255),
    is_active BOOLEAN NOT NULL DEFAULT true,
    display_order INTEGER DEFAULT 0
);

CREATE TABLE IF NOT EXISTS messages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    message VARCHAR(2000),
    timestamp TIMESTAMP NOT NULL,
    read BOOLEAN
);

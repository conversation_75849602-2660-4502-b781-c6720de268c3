package com.ilkeradanur.personal_website.service;

import com.ilkeradanur.personal_website.entity.Timeline;
import com.ilkeradanur.personal_website.repository.TimelineRepository;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class TimelineServiceImpl implements TimelineService {

    private final TimelineRepository timelineRepository;

    public TimelineServiceImpl(TimelineRepository timelineRepository) {
        this.timelineRepository = timelineRepository;
    }

    @Override
    public List<Timeline> getAllTimeline() {
        return timelineRepository.findAll();
    }

    @Override
    public Timeline saveTimeline(Timeline timeline) {
        // Null kontrolü
        if (timeline.getTitle() == null || timeline.getTitle().trim().isEmpty()) {
            throw new IllegalArgumentException("Başlık alanı boş olamaz");
        }
        if (timeline.getType() == null || timeline.getType().trim().isEmpty()) {
            throw new IllegalArgumentException("<PERSON><PERSON><PERSON> alan<PERSON> boş olamaz");
        }

        return timelineRepository.save(timeline);
    }

    @Override
    public void deleteTimeline(Long id) {
        timelineRepository.deleteById(id);
    }

    @Override
    public Timeline getTimelineById(Long id) {
        return timelineRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("Timeline kaydı bulunamadı: " + id));
    }

    @Override
    public List<Timeline> getActiveTimeline() {
        return timelineRepository.findByIsActiveTrueOrderByStartDateDesc();
    }

    @Override
    public List<Timeline> getTimelineByType(String type) {
        return timelineRepository.findByTypeAndIsActiveTrueOrderByStartDateDesc(type);
    }

    @Override
    public Map<String, Long> getTimelineCountByType() {
        return timelineRepository.countTimelineByType().stream()
                .collect(Collectors.toMap(
                    result -> (String) result[0],
                    result -> (Long) result[1]
                ));
    }

    @Override
    public Timeline toggleTimelineStatus(Long id) {
        Timeline timeline = getTimelineById(id);
        timeline.setActive(!timeline.isActive());
        return timelineRepository.save(timeline);
    }
}

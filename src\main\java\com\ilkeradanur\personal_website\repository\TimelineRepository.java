package com.ilkeradanur.personal_website.repository;

import com.ilkeradanur.personal_website.entity.Timeline;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface TimelineRepository extends JpaRepository<Timeline, Long> {
    
    // Aktif timeline kayıtlarını getir
    List<Timeline> findByIsActiveTrueOrderByStartDateDesc();
    
    // Türe göre timeline kayıtlarını getir
    List<Timeline> findByTypeAndIsActiveTrueOrderByStartDateDesc(String type);
    
    // Türe göre timeline kayıtlarını say
    @Query("SELECT t.type, COUNT(t) FROM Timeline t WHERE t.isActive = true GROUP BY t.type")
    List<Object[]> countTimelineByType();
    
    // Son eklenen timeline kayıtlarını getir
    @Query(value = "SELECT * FROM timeline WHERE is_active = 1 ORDER BY id DESC LIMIT 5", nativeQuery = true)
    List<Timeline> findLatest5Timeline();
}

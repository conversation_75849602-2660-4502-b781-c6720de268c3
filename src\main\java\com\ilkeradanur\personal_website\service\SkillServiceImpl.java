package com.ilkeradanur.personal_website.service;

import com.ilkeradanur.personal_website.entity.Skill;
import com.ilkeradanur.personal_website.repository.SkillRepository;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class SkillServiceImpl implements SkillService {

    private final SkillRepository skillRepository;

    public SkillServiceImpl(SkillRepository skillRepository) {
        this.skillRepository = skillRepository;
    }

    @Override
    public List<Skill> getAllSkills() {
        return skillRepository.findAll();
    }

    @Override
    public Skill saveSkill(Skill skill) {
        // Null kontrolü
        if (skill.getName() == null || skill.getName().trim().isEmpty()) {
            throw new IllegalArgumentException("<PERSON>enek adı boş olamaz");
        }

        // <PERSON><PERSON><PERSON> bo<PERSON> "Genel" olarak ayarla
        if (skill.getCategory() == null || skill.getCategory().trim().isEmpty()) {
            skill.setCategory("Genel");
        }

        return skillRepository.save(skill);
    }

    @Override
    public void deleteSkill(Long id) {
        if (!skillRepository.existsById(id)) {
            throw new EntityNotFoundException("Yetenek bulunamadı: " + id);
        }
        skillRepository.deleteById(id);
    }

    @Override
    public List<Skill> getSkillsByCategory(String category) {
        return skillRepository.findByCategory(category);
    }

    @Override
    public Map<String, Long> getSkillCountByCategory() {
        return skillRepository.countSkillsByCategory().stream()
                .collect(Collectors.toMap(
                    row -> (String) row[0],
                    row -> (Long) row[1]
                ));
    }

    @Override
    public List<Skill> getActiveSkills() {
        return skillRepository.findByIsActiveTrue();
    }

    @Override
    public List<Skill> getActiveSkillsByCategory(String category) {
        return skillRepository.findByCategoryAndIsActiveTrue(category);
    }

    @Override
    public List<Skill> getCertifiedSkills() {
        return skillRepository.findCertifiedSkills();
    }

    @Override
    public Skill toggleSkillStatus(Long id) {
        Skill skill = skillRepository.findById(id)
            .orElseThrow(() -> new EntityNotFoundException("Yetenek bulunamadı: " + id));
        skill.setActive(!skill.isActive());
        return skillRepository.save(skill);
    }
}
-- Ad<PERSON> k<PERSON><PERSON><PERSON><PERSON> ekleme (şifre: admin)
INSERT OR REPLACE INTO users (username, password, role, enabled) VALUES
('admin', 'admin', 'ROLE_ADMIN', 1);

-- About bilgileri ekleme
INSERT OR IGNORE INTO about (full_name, title, email, phone, location, birth_date, about_text, github_url, linkedin_url, twitter_url, website_url, is_active, display_order) VALUES
('Ilker Adanur', 'Full Stack Developer', '<EMAIL>', '+90 ************', 'Istanbul, Turkiye', NULL,
'Merhaba! <PERSON>ker <PERSON>nur, tutkulu bir yazilim gelistiricisiyim. Java, Spring Boot, React ve modern web teknolojileri konusunda deneyimliyim. Surekli ogrenmeye ve kendimi gelistirmeye odaklaniyorum.',
'https://github.com/ilkeradanur', 'https://linkedin.com/in/ilkeradanur', 'https://twitter.com/ilkeradanur', 'https://www.ilkeradanur.com', 1, 1);

-- <PERSON><PERSON><PERSON><PERSON> (Skills) tablosuna örnek veriler
INSERT OR IGNORE INTO skills (name, category, description, proficiency, icon, certification_name, certification_url, issuer, issue_date, expiry_date, is_active) VALUES
-- Programlama Dilleri
('Java', 'Programlama Dilleri', 'Nesne yönelimli programlama dili', 90, 'java.png', 'Oracle Certified Professional, Java SE 11 Developer', 'https://www.credly.com/badges/123', 'Oracle', '2023-01-15', '2026-01-15', true),
('Python', 'Programlama Dilleri', 'Yüksek seviyeli programlama dili', 85, 'python.png', 'Python for Data Science', 'https://www.coursera.org/account/accomplishments/123', 'Coursera', '2023-03-20', null, true),
('JavaScript', 'Programlama Dilleri', 'Web programlama dili', 88, 'javascript.png', null, null, null, null, null, true),

-- Web Teknolojileri
('Spring Boot', 'Web Teknolojileri', 'Java tabanlı web framework', 85, 'spring.png', 'Spring Professional Certification', 'https://www.credly.com/badges/456', 'VMware', '2023-06-10', '2026-06-10', true),
('React', 'Web Teknolojileri', 'Frontend kütüphanesi', 82, 'react.png', null, null, null, null, null, true),
('HTML5/CSS3', 'Web Teknolojileri', 'Web sayfası yapılandırma', 90, 'html5.png', null, null, null, null, null, true),

-- Veritabanları
('SQL', 'Veritabanları', 'İlişkisel veritabanı yönetimi', 88, 'sql.png', 'Microsoft SQL Server Certification', 'https://www.credly.com/badges/789', 'Microsoft', '2023-04-05', '2026-04-05', true),
('MongoDB', 'Veritabanları', 'NoSQL veritabanı', 75, 'mongodb.png', null, null, null, null, null, true),

-- DevOps
('Docker', 'DevOps', 'Konteynerizasyon platformu', 80, 'docker.png', 'Docker Certified Associate', 'https://www.credly.com/badges/101', 'Docker', '2023-02-28', '2026-02-28', true),
('Git', 'DevOps', 'Versiyon kontrol sistemi', 85, 'git.png', null, null, null, null, null, true),
('Jenkins', 'DevOps', 'CI/CD aracı', 70, 'jenkins.png', null, null, null, null, null, true);

-- Projeler (Projects) tablosuna örnek veriler
INSERT OR IGNORE INTO projects (title, description, start_date, end_date, status, github_link, demo_link, category, technologies) VALUES
-- Web Projeleri
('Kişisel Web Sitesi', 'Spring Boot ve Thymeleaf kullanılarak geliştirilmiş kişisel web sitesi', '2024-01-01', '2024-03-15', 'COMPLETED', 'https://github.com/username/personal-website', 'https://www.ilkeradanur.com', 'Web Geliştirme', 'Java,Spring Boot,Thymeleaf,SQLite'),
('E-Ticaret Platformu', 'React ve Node.js ile geliştirilmiş e-ticaret platformu', '2023-09-01', '2024-02-28', 'COMPLETED', 'https://github.com/username/ecommerce-platform', 'https://demo-ecommerce.com', 'Web Geliştirme', 'React,Node.js,MongoDB,Express'),
('Blog Uygulaması', 'Django ile geliştirilmiş blog uygulaması', '2024-02-01', null, 'IN_PROGRESS', 'https://github.com/username/blog-app', null, 'Web Geliştirme', 'Python,Django,PostgreSQL'),

-- Mobil Uygulamalar
('Fitness Takip Uygulaması', 'Flutter ile geliştirilmiş fitness takip uygulaması', '2023-11-01', '2024-01-31', 'COMPLETED', 'https://github.com/username/fitness-app', 'https://play.google.com/store/apps/fitness-app', 'Mobil Geliştirme', 'Flutter,Dart,Firebase'),
('Hava Durumu Uygulaması', 'React Native ile geliştirilmiş hava durumu uygulaması', '2024-01-15', null, 'IN_PROGRESS', 'https://github.com/username/weather-app', null, 'Mobil Geliştirme', 'React Native,JavaScript,OpenWeather API'),

-- Veri Bilimi
('Makine Öğrenmesi Projesi', 'Python ile geliştirilmiş tahmin modeli', '2023-12-01', '2024-02-15', 'COMPLETED', 'https://github.com/username/ml-project', null, 'Veri Bilimi', 'Python,Scikit-learn,Pandas,NumPy'),
('Veri Görselleştirme', 'COVID-19 verilerinin görselleştirilmesi', '2024-02-01', null, 'IN_PROGRESS', 'https://github.com/username/covid-visualization', 'https://covid-viz-demo.com', 'Veri Bilimi', 'Python,Matplotlib,Seaborn,Plotly'),

-- DevOps
('CI/CD Pipeline', 'Jenkins ile otomatik deployment pipeline', '2023-10-01', '2023-12-31', 'COMPLETED', 'https://github.com/username/cicd-pipeline', null, 'DevOps', 'Jenkins,Docker,Git,GitHub Actions'),
('Kubernetes Cluster', 'Mikroservis mimarisi için Kubernetes cluster kurulumu', '2024-01-01', null, 'IN_PROGRESS', 'https://github.com/username/k8s-cluster', null, 'DevOps', 'Kubernetes,Docker,Helm,AWS');
<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title th:text="${title}"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Düzenle</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1>Ha<PERSON><PERSON><PERSON><PERSON><PERSON> Düzenle</h1>
                    <a href="/admin/dashboard" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> <PERSON><PERSON>
                    </a>
                </div>

                <!-- Bildir<PERSON><PERSON> -->
                <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
                    <span th:text="${success}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                    <span th:text="${error}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <!-- Hakkımda Düzenleme Formu -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Kişisel Bilgiler</h5>
                    </div>
                    <div class="card-body">
                        <!-- Form object binding ile düzeltildi -->
                        <form th:action="@{/admin/about}" th:object="${about}" method="post">
                            <!-- ID alanı (gizli) -->
                            <input type="hidden" th:field="*{id}" />



                            <!-- Kişisel Bilgiler -->
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="fullName" class="form-label">Ad Soyad <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="fullName" th:field="*{fullName}" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="title" class="form-label">Ünvan <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="title" th:field="*{title}" required>
                                </div>
                            </div>

                            <!-- Hakkımda Metni -->
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="aboutText" class="form-label">Hakkımda Metni <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="aboutText" th:field="*{aboutText}" rows="8" required
                                              placeholder="Kendiniz hakkında detaylı bilgi yazın. Bu metin ana sayfada görünecektir..."></textarea>
                                </div>
                            </div>

                            <div class="mt-4">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-save"></i> Değişiklikleri Kaydet
                                </button>
                                <a href="/admin/dashboard" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> İptal
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
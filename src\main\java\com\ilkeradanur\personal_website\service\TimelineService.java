package com.ilkeradanur.personal_website.service;

import com.ilkeradanur.personal_website.entity.Timeline;
import java.util.List;
import java.util.Map;

public interface TimelineService {
    // Temel CRUD işlemleri
    List<Timeline> getAllTimeline();
    Timeline saveTimeline(Timeline timeline);
    void deleteTimeline(Long id);
    Timeline getTimelineById(Long id);
    
    // Aktif timeline kayıtları
    List<Timeline> getActiveTimeline();
    
    // Türe göre timeline kayıtları
    List<Timeline> getTimelineByType(String type);
    Map<String, Long> getTimelineCountByType();
    
    // Timeline durumu işlemleri
    Timeline toggleTimelineStatus(Long id);
}
